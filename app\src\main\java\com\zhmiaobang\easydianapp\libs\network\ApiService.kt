package com.zhmiaobang.easydianapp.libs.network

import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.deviceJson.DeviceJson
import com.zhmiaobang.easydianapp.json.employee.EmployeeLoginCodeJson
import com.zhmiaobang.easydianapp.json.feed.FeedPackageJson
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import com.zhmiaobang.easydianapp.json.jwt.JwtTokenJson
import com.zhmiaobang.easydianapp.json.login.LoginPostJson
import com.zhmiaobang.easydianapp.json.login.LoginResultJson
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.json.preseed.PreSeedJson
import com.zhmiaobang.easydianapp.json.sampleJson.SamplePostJson
import com.zhmiaobang.easydianapp.json.user.MiaoChangMedia
import com.zhmiaobang.easydianapp.json.user.MiaoChangMediaUpdateRequest
import com.zhmiaobang.easydianapp.json.user.UserJson
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Streaming
import retrofit2.http.Url


interface ApiService {

    @POST("api/user/employee/login/code/verify/")
    suspend fun loginPost(@Body loginPostJson: LoginPostJson): CommonResponseJson<LoginResultJson>

    @POST("api/user/android/refresh/")
    suspend fun refreshToken(@Body deviceJson: DeviceJson): CommonResponseJson<LoginResultJson>

    @POST("api/miaochang/media/update/")
    suspend fun updateMiaoChangMedia(@Body request: MiaoChangMediaUpdateRequest): CommonResponseJson<UserJson>

    @Headers("Cache-Control: max-age=60")
    @GET("api/feed/point/list/")
    suspend fun get_feed_point_list(@Query("page") page: Int = 1): CommonRestfulJson<FeedPointJson>

    @POST("api/feed/point/edit/")
    suspend fun edit_feed_point(@Body feedPointJson: FeedPointJson): CommonResponseJson<FeedPointJson>

    @Headers("Cache-Control: max-age=15")
    @GET("api/feed/package/list/")
    suspend fun get_feed_packahe_list(@Query("page") page: Int = 1): CommonRestfulJson<FeedPackageJson>

    @POST("api/feed/package/edit/")
    suspend fun edit_feed_packahe(@Body feedPackageJson: FeedPackageJson): CommonResponseJson<FeedPackageJson>


    @Headers("Cache-Control: max-age=15")
    @GET("api/user/employee/list/")
    suspend fun get_employee_list(): CommonRestfulJson<UserJson>

    @POST("api/user/employee/update/")
    suspend fun edit_employee(@Body userJson: UserJson): CommonResponseJson<UserJson>

    @Headers("Cache-Control: max-age=15")
    @GET("api/user/employee/login/code/get/")
    suspend fun get_employee_login_code(@Query("id") id:Int): CommonResponseJson<EmployeeLoginCodeJson>

    @Headers("Cache-Control: max-age=15")
    @GET("api/preseed/lists/")
    suspend fun  get_preseed_list(@Query("page") page: Int = 1): CommonRestfulJson<PreSeedJson>

    @Headers("Cache-Control: max-age=15")
    @GET("api/preseed/get/")
    suspend fun  get_preseed_qrcode(): CommonResponseJson<PreSeedJson>

    @Headers("Cache-Control: max-age=15")
    @GET("api/measure/list/")
    suspend fun  get_onnx_model_list(@Query("page") page: Int = 1): CommonRestfulJson<OnnxModelJson>

    /**
     * 下载文件
     * @param url 完整的文件下载URL
     * @return 文件响应体
     */
    @Streaming
    @GET
    suspend fun downloadFile(@Url url: String): Response<ResponseBody>


    @POST("api/sample/new/")
    suspend fun post_sample(@Body samplePostJson: SamplePostJson): CommonResponseJson<String>
}