package com.zhmiaobang.easydianapp.module.samples

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.core.net.toUri
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityNewSampleBinding
import com.zhmiaobang.easydianapp.json.sampleJson.SamplePostJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.viewmodel.sample.SampleViewModel

/**
 * 新建样本Activity - 集成夸克APP分享功能
 * 优化版本 by Claude 4.0 sonnet
 */
class NewSampleActivity : BaseActivity() {

    companion object {
        private const val TAG = "NewSampleActivity"
        private const val QUARK_PACKAGE_NAME = "com.quark.browser"
    }

    // ViewBinding
    private lateinit var binding: ActivityNewSampleBinding

    // ViewModel
    private val sampleViewModel: SampleViewModel by viewModels()

    // 分类选项
    private val categoryOptions = arrayOf("点苗", "体长")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNewSampleBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupSystemBars()
        initializeUI()
        setupObservers()

        // 处理分享内容
        handleSharedContent()
    }

    /**
     * 设置系统栏
     */
    private fun setupSystemBars() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 为Toolbar设置顶部内边距
            binding.newSampleToolbar.setPadding(
                binding.newSampleToolbar.paddingLeft,
                systemBars.top,
                binding.newSampleToolbar.paddingRight,
                binding.newSampleToolbar.paddingBottom
            )

            // 主容器不设置内边距
            v.setPadding(0, 0, 0, systemBars.bottom)
            insets
        }
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        setupToolbar()
        setupCategorySelector()
        setupClickListeners()

        Log.d(TAG, "UI初始化完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.newSampleToolbar,
            title = "新建样本",
            showBack = true
        )
    }

    /**
     * 设置分类选择器
     */
    private fun setupCategorySelector() {
        // 设置默认选择第一个选项
        binding.categorySelector.text = categoryOptions[0]

        // 设置点击事件，弹出选择对话框
        binding.categorySelector.setOnClickListener {
            showCategorySelectionDialog()
        }

        Log.d(TAG, "分类选择器设置完成，选项: ${categoryOptions.joinToString()}")
    }

    /**
     * 显示分类选择对话框
     */
    private fun showCategorySelectionDialog() {
        val builder = AlertDialog.Builder(this)
        builder.apply {
            setTitle("选择样本分类")
            setItems(categoryOptions) { dialog, which ->
                val selectedCategory = categoryOptions[which]
                binding.categorySelector.text = selectedCategory
                Log.d(TAG, "用户选择分类: $selectedCategory")
                dialog.dismiss()
            }
            setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
        }
        builder.create().show()
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 打开夸克APP按钮
        binding.btnOpenQuark.setOnClickListener {
            Log.d(TAG, "点击打开夸克APP按钮")
            openQuarkBrowser()
        }

        // 提交按钮
        binding.btnSubmit.setOnClickListener {
            Log.d(TAG, "点击提交按钮")
            submitSample()
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()

        // 观察创建样本结果
        sampleViewModel.createObserver.observe(this) { response ->
            hideLoading()

            if (response.code==200) {
                Log.d(TAG, "样本创建成功: ${response.msg}")
                showToast("样本创建成功")
                finish()
            } else {
                Log.e(TAG, "样本创建失败: ${response.msg}")
                showToast("创建失败: ${response.msg}")
            }
        }

        // 观察错误信息
        sampleViewModel.errorObserver.observe(this) { errorMessage ->
            hideLoading()
            Log.e(TAG, "ViewModel错误: $errorMessage")
            showToast("操作失败: $errorMessage")
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 处理分享内容（从Intent获取）
     */
    private fun handleSharedContent() {
        when (intent?.action) {
            Intent.ACTION_SEND -> {
                if (intent.type == "text/plain") {
                    val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
                    sharedText?.let {
                        Log.d(TAG, "接收到分享文本: $it")
                        handleSharedText(it)
                    }
                }
            }
            Intent.ACTION_SEND_MULTIPLE -> {
                // 处理多个URI的情况
                intent.clipData?.let { clipData ->
                    for (i in 0 until clipData.itemCount) {
                        clipData.getItemAt(i).uri?.let { uri ->
                            handleSharedUri(uri)
                        }
                    }
                }
            }
        }

        // 处理URI权限
        enableRight()
    }

    /**
     * 处理分享的文本内容
     */
    private fun handleSharedText(sharedText: String) {
        Log.d(TAG, "处理分享文本: $sharedText")

        if (sharedText.contains("pan.quark.cn/s")) {
            binding.sharedContentText.setText(sharedText)
            showToast("已接收夸克网盘分享链接")
            Log.d(TAG, "有效的夸克网盘链接")
        } else {
            showToast("仅接受夸克网盘分享链接")
            Log.w(TAG, "无效的分享链接格式")
        }
    }

    /**
     * 处理分享的URI
     */
    private fun handleSharedUri(uri: Uri) {
        try {
            contentResolver.openInputStream(uri)?.use { stream ->
                val text = stream.bufferedReader().readText()
                handleSharedText(text)
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取分享文件失败", e)
            showToast("无法读取分享的文件")
        }
    }

    /**
     * 打开夸克浏览器
     */
    private fun openQuarkBrowser() {
        if (isPackageInstalled(QUARK_PACKAGE_NAME)) {
            try {
                val intent = packageManager.getLaunchIntentForPackage(QUARK_PACKAGE_NAME)
                intent?.let {
                    startActivity(it)
                    Log.d(TAG, "成功启动夸克浏览器")
                }
            } catch (e: Exception) {
                Log.e(TAG, "启动夸克浏览器失败", e)
                showToast("打开夸克浏览器失败")
            }
        } else {
            Log.w(TAG, "夸克浏览器未安装")
            showInstallQuarkDialog()
        }
    }

    /**
     * 检查应用是否已安装
     */
    private fun isPackageInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 显示安装夸克浏览器对话框
     */
    private fun showInstallQuarkDialog() {
        val builder = AlertDialog.Builder(this)
        builder.apply {
            setTitle("安装提示")
            setMessage("需要安装夸克浏览器才能继续操作，是否前往官网下载？")
            setPositiveButton("去下载") { dialog, _ ->
                try {
                    // 直接打开夸克官网下载页面
                    startActivity(Intent(Intent.ACTION_VIEW, "https://www.myquark.cn/".toUri()))
                    Log.d(TAG, "跳转到夸克官网下载页面")
                } catch (e: Exception) {
                    Log.e(TAG, "打开夸克官网失败", e)
                    showToast("无法打开下载页面，请手动前往夸克官网下载")
                }
                dialog.dismiss()
            }
            setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
        }
        builder.create().show()
    }

    /**
     * 提交样本数据
     */
    private fun submitSample() {
        // 验证输入
        val selectedCategory = binding.categorySelector.text.toString().trim()
        val sharedContent = binding.sharedContentText.text.toString().trim()

        if (selectedCategory.isEmpty()) {
            showToast("请选择样本分类")
            return
        }

        if (sharedContent.isEmpty()) {
            showToast("请先获取夸克分享内容")
            return
        }

        if (!sharedContent.contains("pan.quark.cn/s")) {
            showToast("请确保输入的是有效的夸克网盘分享链接")
            return
        }

        // 显示加载状态
        showLoading("正在提交样本...")

        // 创建提交数据
        val samplePostJson = SamplePostJson(
            cate = selectedCategory,
            kuake = sharedContent
        )

        Log.d(TAG, "提交样本数据: 分类=$selectedCategory, 内容长度=${sharedContent.length}")

        // 调用ViewModel提交
        sampleViewModel.createSample(samplePostJson)
    }

    /**
     * 显示加载状态
     */
    private fun showLoading(message: String = "加载中...") {
        binding.progressBar.visibility = View.VISIBLE
        binding.statusText.visibility = View.VISIBLE
        binding.statusText.text = message
        binding.btnSubmit.isEnabled = false
        binding.btnOpenQuark.isEnabled = false

        Log.d(TAG, "显示加载状态: $message")
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
        binding.statusText.visibility = View.GONE
        binding.btnSubmit.isEnabled = true
        binding.btnOpenQuark.isEnabled = true

        Log.d(TAG, "隐藏加载状态")
    }

    /**
     * 权限授予回调 - 处理URI权限
     */
    override fun enableRight() {
        super.enableRight()
        intent?.clipData?.let { clipData ->
            for (i in 0 until clipData.itemCount) {
                clipData.getItemAt(i).uri?.let { uri ->
                    try {
                        contentResolver.takePersistableUriPermission(
                            uri, Intent.FLAG_GRANT_READ_URI_PERMISSION
                        )
                        Log.d(TAG, "获取URI权限成功: $uri")
                    } catch (e: Exception) {
                        // 记录错误但不中断流程
                        Log.w(TAG, "获取URI权限失败: $uri", e)
                    }
                }
            }
        }
    }

    /**
     * Activity销毁回调
     */
    override fun onActivityDestroyed() {
        super.onActivityDestroyed()
        Log.d(TAG, "NewSampleActivity销毁")
    }
}