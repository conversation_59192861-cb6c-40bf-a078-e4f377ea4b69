package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemLoadingBinding
import com.zhmiaobang.easydianapp.databinding.ItemErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemSampleBinding
import com.zhmiaobang.easydianapp.json.sampleJson.SampleJson
import com.zhmiaobang.easydianapp.utils.LoadingState
import java.text.SimpleDateFormat
import java.util.*

/**
 * 样本列表适配器 - 支持分页加载
 * 用于在 SamplelistsActivity 中展示样本列表
 *
 * 功能特性：
 * - 展示样本基本信息（苗场名称、人员信息、分类、状态、时间）
 * - 支持点击查看详情
 * - 支持分页加载和加载状态显示
 * - 状态指示器和分类标签显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class SampleAdapter(
    private var samples: MutableList<SampleJson> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "SampleAdapter"

        // ViewType常量
        private const val TYPE_SAMPLE = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String? = null

    // 时间格式化器
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())

    // 点击事件监听器接口
    interface OnItemClickListener {
        fun onItemClick(sample: SampleJson, position: Int)
        fun onRetryClick() // 重试点击事件
    }

    private var itemClickListener: OnItemClickListener? = null

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            position < samples.size -> TYPE_SAMPLE
            loadingState == LoadingState.LOADING -> TYPE_LOADING
            loadingState == LoadingState.ERROR -> TYPE_ERROR
            loadingState == LoadingState.NO_MORE -> TYPE_NO_MORE
            else -> TYPE_SAMPLE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            TYPE_SAMPLE -> {
                val binding = ItemSampleBinding.inflate(inflater, parent, false)
                SampleViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingBinding.inflate(inflater, parent, false)
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemErrorBinding.inflate(inflater, parent, false)
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreBinding.inflate(inflater, parent, false)
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is SampleViewHolder -> {
                if (position < samples.size) {
                    holder.bind(samples[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage ?: "加载失败")
            }
        }
    }

    override fun getItemCount(): Int {
        return samples.size + if (shouldShowLoadingItem()) 1 else 0
    }

    /**
     * 是否应该显示加载项
     */
    private fun shouldShowLoadingItem(): Boolean {
        return loadingState != LoadingState.IDLE && samples.isNotEmpty()
    }

    /**
     * 样本ViewHolder
     */
    inner class SampleViewHolder(private val binding: ItemSampleBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION && position < samples.size) {
                    itemClickListener?.onItemClick(samples[position], position)
                }
            }
        }

        fun bind(sample: SampleJson) {
            binding.apply {
                // 苗场名称
                tvMiaochangName.text = sample.miaochang.name

                // 样本人员信息
                val personInfo = "${sample.samplePerson.nickname} (${sample.samplePerson.phone})"
                tvSamplePerson.text = personInfo

                // 分类标签
                tvCategory.text = sample.cate
                setCategoryStyle(tvCategory, sample.cate)

                // 状态
                tvStatus.text = getStatusText(sample.status)
                setStatusStyle(tvStatus, sample.status)

                // 创建时间
                tvCreateTime.text = formatTime(sample.createTime)
            }
        }

        /**
         * 设置分类样式
         */
        private fun setCategoryStyle(textView: android.widget.TextView, category: String) {
            val context = textView.context
            when (category) {
                "点苗" -> {
                    textView.setTextColor(ContextCompat.getColor(context, R.color.hIndex_blue))
                    textView.setBackgroundResource(R.drawable.rounded_corner_background)
                }
                "体长" -> {
                    textView.setTextColor(ContextCompat.getColor(context, R.color.hIndex_green))
                    textView.setBackgroundResource(R.drawable.rounded_corner_background)
                }
                else -> {
                    textView.setTextColor(ContextCompat.getColor(context, R.color.hIndex_text_secondary))
                    textView.setBackgroundResource(R.drawable.rounded_corner_background)
                }
            }
        }

        /**
         * 设置状态样式
         */
        private fun setStatusStyle(textView: android.widget.TextView, status: Int) {
            val context = textView.context
            when (status) {
                1 -> {
                    textView.setBackgroundColor(ContextCompat.getColor(context, R.color.hIndex_green))
                }
                0 -> {
                    textView.setBackgroundColor(ContextCompat.getColor(context, R.color.hIndex_orange))
                }
                else -> {
                    textView.setBackgroundColor(ContextCompat.getColor(context, R.color.hIndex_text_hint))
                }
            }
        }

        /**
         * 获取状态文字
         */
        private fun getStatusText(status: Int): String {
            return when (status) {
                1 -> "已处理"
                0 -> "待处理"
                else -> "未知"
            }
        }

        /**
         * 格式化时间
         */
        private fun formatTime(timeString: String): String {
            return try {
                // 假设服务器返回的时间格式为 "yyyy-MM-dd HH:mm:ss"
                val serverFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val date = serverFormat.parse(timeString)
                date?.let { dateFormatter.format(it) } ?: timeString
            } catch (e: Exception) {
                timeString // 如果解析失败，返回原始字符串
            }
        }
    }

    /**
     * 加载中ViewHolder
     */
    inner class LoadingViewHolder(binding: ItemLoadingBinding) : RecyclerView.ViewHolder(binding.root)

    /**
     * 错误ViewHolder
     */
    inner class ErrorViewHolder(private val binding: ItemErrorBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
        }
    }

    /**
     * 无更多数据ViewHolder
     */
    inner class NoMoreViewHolder(binding: ItemNoMoreBinding) : RecyclerView.ViewHolder(binding.root)

    // ==================== 数据管理方法 ====================

    /**
     * 设置新数据（清空原有数据）
     */
    fun setData(newSamples: List<SampleJson>) {
        samples.clear()
        samples.addAll(newSamples)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 添加更多数据（分页加载）
     */
    fun addData(newSamples: List<SampleJson>) {
        val startPosition = samples.size
        samples.addAll(newSamples)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newSamples.size)
    }

    /**
     * 设置加载状态
     */
    fun setLoadingState(state: LoadingState, errorMsg: String? = null) {
        val oldState = loadingState
        loadingState = state
        errorMessage = errorMsg

        // 通知状态项变化
        if (samples.isNotEmpty()) {
            if (oldState != LoadingState.IDLE) {
                notifyItemChanged(samples.size)
            }
            if (state != LoadingState.IDLE) {
                notifyItemInserted(samples.size)
            }
        }
    }

    /**
     * 清空数据
     */
    fun clearData() {
        samples.clear()
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 获取当前数据数量
     */
    fun getDataCount(): Int = samples.size
}
