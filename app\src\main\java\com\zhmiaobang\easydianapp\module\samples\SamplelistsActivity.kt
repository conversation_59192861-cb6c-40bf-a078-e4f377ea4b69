package com.zhmiaobang.easydianapp.module.samples

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.SampleAdapter
import com.zhmiaobang.easydianapp.databinding.ActivitySamplelistsBinding
import com.zhmiaobang.easydianapp.json.sampleJson.SampleJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.viewmodel.sample.SampleViewModel
import kotlin.collections.isNotEmpty

/**
 * 样本列表Activity
 *
 * 功能特性：
 * - 展示样本列表数据（苗场名称、人员信息、分类、状态、时间）
 * - 支持分页加载和下拉刷新
 * - 支持点击查看详情
 * - 完整的加载状态和错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class SamplelistsActivity : BaseActivity() {

    companion object {
        private const val TAG = "SamplelistsActivity"
    }

    // ViewBinding
    private lateinit var binding: ActivitySamplelistsBinding

    // ViewModel
    private val sampleViewModel: SampleViewModel by viewModels()

    // Adapter
    private lateinit var sampleAdapter: SampleAdapter

    // 分页管理
    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySamplelistsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupSystemBars()
        initializeUI()
        setupObservers()

        Log.d(TAG, "SamplelistsActivity创建完成，观察者已设置")
    }

    /**
     * 设置系统栏
     */
    private fun setupSystemBars() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 为Toolbar设置顶部内边距
            binding.sampleListToolbar.setPadding(
                binding.sampleListToolbar.paddingLeft,
                systemBars.top,
                binding.sampleListToolbar.paddingRight,
                binding.sampleListToolbar.paddingBottom
            )

            // 主容器不设置内边距
            v.setPadding(0, 0, 0, systemBars.bottom)
            insets
        }
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()

        loadSampleData()
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        Log.d(TAG, "开始设置观察者")

        // 观察样本列表数据
        sampleViewModel.listObserver.observe(this) { response ->
            hideSwipeRefresh()
            isLoading = false

            if (response.success) {
                val samples = response.data ?: emptyList()
                Log.d(TAG, "获取样本列表成功，数量: ${samples.size}")

                if (currentPage == 1) {
                    // 第一页数据，替换现有数据
                    sampleAdapter.setData(samples)
                    updateEmptyState(samples.isEmpty())
                } else {
                    // 分页数据，追加到现有数据
                    sampleAdapter.addData(samples)
                }

                // 判断是否还有更多数据
                hasMoreData = samples.isNotEmpty() && samples.size >= 20 // 假设每页20条
                if (!hasMoreData) {
                    sampleAdapter.setLoadingState(LoadingState.NO_MORE)
                }

            } else {
                Log.e(TAG, "获取样本列表失败: ${response.message}")
                if (currentPage == 1) {
                    updateEmptyState(true)
                } else {
                    sampleAdapter.setLoadingState(LoadingState.ERROR, response.message)
                }
                showToast("获取数据失败: ${response.message}")
            }
        }

        // 观察错误信息
        sampleViewModel.errorObserver.observe(this) { errorMessage ->
            hideSwipeRefresh()
            isLoading = false
            Log.e(TAG, "ViewModel错误: $errorMessage")

            if (currentPage == 1) {
                updateEmptyState(true)
            } else {
                sampleAdapter.setLoadingState(LoadingState.ERROR, errorMessage)
            }
            showToast("操作失败: $errorMessage")
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.sampleListToolbar,
            title = "样本列表",
            showBack = true
        )
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        sampleAdapter = SampleAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.sampleRecyclerView.apply {
            this.layoutManager = layoutManager
            adapter = sampleAdapter
            setHasFixedSize(true)

            // 添加滚动监听器，实现分页加载
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // 当滚动到底部时加载更多数据
                    if (!isLoading && hasMoreData && dy > 0) {
                        if (visibleItemCount + firstVisibleItemPosition >= totalItemCount - 3) {
                            loadMoreData()
                        }
                    }
                }
            })
        }

        // 设置点击事件监听器
        sampleAdapter.setOnItemClickListener(object : SampleAdapter.OnItemClickListener {
            override fun onItemClick(sample: SampleJson, position: Int) {
                Log.d(TAG, "点击样本: ${sample.miaochang.name} - ${sample.cate}")
                // TODO: 跳转到样本详情页面
                showToast("点击了样本: ${sample.miaochang.name}")
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                loadMoreData()
            }
        })

        Log.d(TAG, "RecyclerView设置完成")
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(
                R.color.hIndex_blue,
                R.color.hIndex_green,
                R.color.hIndex_orange
            )
            setOnRefreshListener {
                Log.d(TAG, "触发下拉刷新")
                refreshData()
            }
        }
    }

    /**
     * 加载样本数据（第一页）
     */
    private fun loadSampleData() {
        if (isLoading) return

        Log.d(TAG, "开始加载样本数据")
        currentPage = 1
        isLoading = true
        hasMoreData = true

        sampleAdapter.setLoadingState(LoadingState.LOADING)
        sampleViewModel.get_list(currentPage)
    }
    /**
     * 加载更多数据（分页）
     */
    private fun loadMoreData() {
        if (isLoading || !hasMoreData) return

        Log.d(TAG, "开始加载更多数据，页码: ${currentPage + 1}")
        currentPage++
        isLoading = true

        sampleAdapter.setLoadingState(LoadingState.LOADING)
        sampleViewModel.get_list(currentPage)
    }

    /**
     * 刷新数据
     */
    private fun refreshData() {
        Log.d(TAG, "刷新数据")
        currentPage = 1
        isLoading = true
        hasMoreData = true

        sampleAdapter.clearData()
        sampleViewModel.get_list(currentPage)
    }

    /**
     * 隐藏下拉刷新指示器
     */
    private fun hideSwipeRefresh() {
        if (binding.swipeRefreshLayout.isRefreshing) {
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    /**
     * 更新空状态显示
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.emptyStateLayout.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.sampleRecyclerView.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }

    /**
     * Activity销毁回调
     */
    override fun onActivityDestroyed() {
        super.onActivityDestroyed()
        Log.d(TAG, "SamplelistsActivity销毁")
    }
}