<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".module.samples.NewSampleActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/new_sample_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="新建样本"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Main Content ScrollView -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/new_sample_toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 分类选择区域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/material_on_surface_stroke">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="样本分类"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/material_on_surface_emphasis_high_type"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/category_selector"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:background="@drawable/rounded_corner_background"
                        android:drawableEnd="@drawable/ic_expand_more_24"
                        android:drawablePadding="12dp"
                        android:gravity="center_vertical"
                        android:hint="请选择分类"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:text="点苗"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 分享内容显示区域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/material_on_surface_stroke">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="夸克分享内容"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/material_on_surface_emphasis_high_type"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/content_input_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="夸克分享的内容将显示在这里"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/shared_content_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minLines="4"
                            android:maxLines="8"
                            android:gravity="top|start"
                            android:inputType="textMultiLine"
                            android:scrollbars="vertical"
                            android:textSize="14sp"
                            android:background="@android:color/transparent" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 操作按钮区域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/material_on_surface_stroke">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="获取分享内容"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/material_on_surface_emphasis_high_type"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_open_quark"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:text="打开夸克APP"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:icon="@drawable/ic_open_in_new_24"
                        app:iconGravity="textStart"
                        app:iconPadding="8dp"
                        style="@style/Widget.MaterialComponents.Button.Icon" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="点击按钮打开夸克APP，完成操作后分享内容将自动填入上方文本框"
                        android:textSize="12sp"
                        android:textColor="@color/material_on_surface_emphasis_medium"
                        android:layout_marginTop="8dp"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 提交区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="提交样本"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:icon="@drawable/ic_check_24"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    style="@style/Widget.MaterialComponents.Button" />

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    style="?android:attr/progressBarStyle" />

                <TextView
                    android:id="@+id/status_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textSize="14sp"
                    android:textColor="@color/material_on_surface_emphasis_medium"
                    android:visibility="gone"
                    tools:text="正在提交..." />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>