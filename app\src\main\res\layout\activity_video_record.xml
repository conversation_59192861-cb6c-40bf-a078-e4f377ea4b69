<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".module.video.VideoRecordActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@android:color/white"
        app:title="视频录制"
        app:titleTextColor="@android:color/white" />

    <!-- 相机预览区域 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/video_preview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/control_container"
        app:layout_constraintDimensionRatio="3:4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:scaleType="fillCenter" />


    <!-- 底部控制区域 -->
    <LinearLayout
        android:id="@+id/control_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/black_alpha_50"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 录制进度 -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/recording_progress"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:indicatorColor="@color/primary_color"
            app:trackColor="@color/white_alpha_30" />

        <!-- 录制按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/record_button"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="16dp"
            android:backgroundTint="@color/primary_color"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:text="开始录制"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            app:cornerRadius="60dp"
            app:icon="@drawable/ic_videocam"
            app:iconGravity="top"
            app:iconSize="32dp"
            app:iconTint="@color/white" />

        <!-- 状态文本 -->
        <TextView
            android:id="@+id/status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="准备录制"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 时间显示 -->
        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="@color/white_alpha_70"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>