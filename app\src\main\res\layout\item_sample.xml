<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/material_on_surface_stroke"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 头部信息：苗场名称和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_miaochang_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/hIndex_text_primary"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="示例苗场" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_status_indicator_active"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                tools:text="已处理" />

        </LinearLayout>

        <!-- 样本人员信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_person"
                android:tint="@color/hIndex_text_secondary"
                android:layout_marginEnd="6dp" />

            <TextView
                android:id="@+id/tv_sample_person"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="14sp"
                android:textColor="@color/hIndex_text_secondary"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="张三 (13800138000)" />

        </LinearLayout>

        <!-- 分类和时间信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_corner_background"
                android:paddingHorizontal="10dp"
                android:paddingVertical="4dp"
                android:textSize="12sp"
                android:textColor="@color/hIndex_blue"
                android:textStyle="bold"
                tools:text="点苗" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_schedule"
                android:tint="@color/hIndex_text_hint"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/tv_create_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/hIndex_text_hint"
                tools:text="2024-01-15 10:30" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
