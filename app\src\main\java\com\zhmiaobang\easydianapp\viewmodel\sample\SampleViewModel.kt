package com.zhmiaobang.easydianapp.viewmodel.sample

import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.sampleJson.SampleJson
import com.zhmiaobang.easydianapp.json.sampleJson.SamplePostJson
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

class SampleViewModel: BaseViewModel() {
    val createObserver: MutableLiveData<CommonResponseJson<String>> by lazy {MutableLiveData<CommonResponseJson<String>>() }

    fun createSample(samplePostJson: SamplePostJson)=launch({
        createObserver.postValue(RetrofitClient.apiService.post_sample(samplePostJson))
    })

    val listObserver: MutableLiveData<CommonRestfulJson<SampleJson> by lazy { MutableLiveData<CommonRestfulJson<SampleJson>>() }

    fun get_list(page:Int)=launch({
        listObserver.postValue(RetrofitClient.apiService.get_sample_list(page))
    })
}